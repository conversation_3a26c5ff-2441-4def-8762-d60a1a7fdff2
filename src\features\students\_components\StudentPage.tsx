import * as React from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { PlusIcon } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { StudentStats } from './StudentStats'
import { StudentTable } from './StudentTable'
import { StudentUpdate } from './StudentUpdate'
import { StudentDelete } from './StudentDelete'
import type { StudentId } from '@/lib/types'

export function StudentPage() {
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = React.useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [selectedStudentId, setSelectedStudentId] = React.useState<StudentId | null>(null)

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold">Students</h1>
          <p className="text-muted-foreground">
            Manage student records for the current academic year
          </p>
        </div>
        <Button asChild>
          <Link to="/dashboard/students/create">
            <PlusIcon />
            Add Student
          </Link>
        </Button>
      </div>

      <StudentStats />

      <StudentTable
        onEdit={(studentId: StudentId) => {
          setSelectedStudentId(studentId)
          setIsUpdateDialogOpen(true)
        }}
        onDelete={(studentId: StudentId) => {
          setSelectedStudentId(studentId)
          setIsDeleteDialogOpen(true)
        }}
      />

      {selectedStudentId && (
        <StudentUpdate
          studentId={selectedStudentId}
          open={isUpdateDialogOpen}
          onOpenChange={(open: boolean) => {
            setIsUpdateDialogOpen(open)
            if (!open) {
              setSelectedStudentId(null)
            }
          }}
        />
      )}

      {selectedStudentId && (
        <StudentDelete
          studentId={selectedStudentId}
          open={isDeleteDialogOpen}
          onOpenChange={(open: boolean) => {
            setIsDeleteDialogOpen(open)
            if (!open) {
              setSelectedStudentId(null)
            }
          }}
        />
      )}
    </div>
  )
}
