// Export main components
export { StudentPage } from './_components/StudentPage'
export { StudentStats } from './_components/StudentStats'
export { StudentCreate } from './_components/StudentCreate'
export { StudentUpdate } from './_components/StudentUpdate'
export { StudentTable } from './_components/StudentTable'
export { StudentDelete } from './_components/StudentDelete'

// Export types
export type { StudentDoc, StudentId, StudentInput } from '@/lib/types'

// Export schemas
export { studentSchema, studentEnrollmentSchema } from './lib/schema'

// Export utilities
export {
  getFullName,
  getFullNameLastFirst,
  calculateAge,
  formatLRN,
  isValidLRN,
  formatAddress,
  getStatusVariant,
  getStatusText,
} from './lib/utils'
