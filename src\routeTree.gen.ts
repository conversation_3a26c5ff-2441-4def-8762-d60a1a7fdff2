/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DashboardRouteImport } from './routes/dashboard/route'
import { Route as IndexImport } from './routes/index'
import { Route as DashboardIndexImport } from './routes/dashboard/index'
import { Route as DashboardSettingsImport } from './routes/dashboard/settings'
import { Route as DashboardSectionsImport } from './routes/dashboard/sections'
import { Route as DashboardStudentsRouteImport } from './routes/dashboard/students/route'
import { Route as DashboardStudentsCreateImport } from './routes/dashboard/students/create'

// Create/Update Routes

const DashboardRouteRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardIndexRoute = DashboardIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardSettingsRoute = DashboardSettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardSectionsRoute = DashboardSectionsImport.update({
  id: '/sections',
  path: '/sections',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardStudentsRouteRoute = DashboardStudentsRouteImport.update({
  id: '/students',
  path: '/students',
  getParentRoute: () => DashboardRouteRoute,
} as any)

const DashboardStudentsCreateRoute = DashboardStudentsCreateImport.update({
  id: '/create',
  path: '/create',
  getParentRoute: () => DashboardStudentsRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/students': {
      id: '/dashboard/students'
      path: '/students'
      fullPath: '/dashboard/students'
      preLoaderRoute: typeof DashboardStudentsRouteImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/sections': {
      id: '/dashboard/sections'
      path: '/sections'
      fullPath: '/dashboard/sections'
      preLoaderRoute: typeof DashboardSectionsImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/settings': {
      id: '/dashboard/settings'
      path: '/settings'
      fullPath: '/dashboard/settings'
      preLoaderRoute: typeof DashboardSettingsImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexImport
      parentRoute: typeof DashboardRouteImport
    }
    '/dashboard/students/create': {
      id: '/dashboard/students/create'
      path: '/create'
      fullPath: '/dashboard/students/create'
      preLoaderRoute: typeof DashboardStudentsCreateImport
      parentRoute: typeof DashboardStudentsRouteImport
    }
  }
}

// Create and export the route tree

interface DashboardStudentsRouteRouteChildren {
  DashboardStudentsCreateRoute: typeof DashboardStudentsCreateRoute
}

const DashboardStudentsRouteRouteChildren: DashboardStudentsRouteRouteChildren =
  {
    DashboardStudentsCreateRoute: DashboardStudentsCreateRoute,
  }

const DashboardStudentsRouteRouteWithChildren =
  DashboardStudentsRouteRoute._addFileChildren(
    DashboardStudentsRouteRouteChildren,
  )

interface DashboardRouteRouteChildren {
  DashboardStudentsRouteRoute: typeof DashboardStudentsRouteRouteWithChildren
  DashboardSectionsRoute: typeof DashboardSectionsRoute
  DashboardSettingsRoute: typeof DashboardSettingsRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
}

const DashboardRouteRouteChildren: DashboardRouteRouteChildren = {
  DashboardStudentsRouteRoute: DashboardStudentsRouteRouteWithChildren,
  DashboardSectionsRoute: DashboardSectionsRoute,
  DashboardSettingsRoute: DashboardSettingsRoute,
  DashboardIndexRoute: DashboardIndexRoute,
}

const DashboardRouteRouteWithChildren = DashboardRouteRoute._addFileChildren(
  DashboardRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/dashboard/students': typeof DashboardStudentsRouteRouteWithChildren
  '/dashboard/sections': typeof DashboardSectionsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/students/create': typeof DashboardStudentsCreateRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dashboard/students': typeof DashboardStudentsRouteRouteWithChildren
  '/dashboard/sections': typeof DashboardSectionsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard': typeof DashboardIndexRoute
  '/dashboard/students/create': typeof DashboardStudentsCreateRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/dashboard/students': typeof DashboardStudentsRouteRouteWithChildren
  '/dashboard/sections': typeof DashboardSectionsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/students/create': typeof DashboardStudentsCreateRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/dashboard/students'
    | '/dashboard/sections'
    | '/dashboard/settings'
    | '/dashboard/'
    | '/dashboard/students/create'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/dashboard/students'
    | '/dashboard/sections'
    | '/dashboard/settings'
    | '/dashboard'
    | '/dashboard/students/create'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/dashboard/students'
    | '/dashboard/sections'
    | '/dashboard/settings'
    | '/dashboard/'
    | '/dashboard/students/create'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRouteRoute: typeof DashboardRouteRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRouteRoute: DashboardRouteRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/dashboard"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard/route.tsx",
      "children": [
        "/dashboard/students",
        "/dashboard/sections",
        "/dashboard/settings",
        "/dashboard/"
      ]
    },
    "/dashboard/students": {
      "filePath": "dashboard/students/route.tsx",
      "parent": "/dashboard",
      "children": [
        "/dashboard/students/create"
      ]
    },
    "/dashboard/sections": {
      "filePath": "dashboard/sections.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/settings": {
      "filePath": "dashboard/settings.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/": {
      "filePath": "dashboard/index.tsx",
      "parent": "/dashboard"
    },
    "/dashboard/students/create": {
      "filePath": "dashboard/students/create.tsx",
      "parent": "/dashboard/students"
    }
  }
}
ROUTE_MANIFEST_END */
